import { defineMessages } from 'react-intl';

export const welcomeModalMessages = defineMessages({
  selectLanguage: {
    id: 'welcomeModal.selectLanguage',
    defaultMessage: 'Select Language',
  },
  choosePreferredLanguage: {
    id: 'welcomeModal.choosePreferredLanguage',
    defaultMessage: 'Choose your preferred language for the app',
  },
  earnRewards: {
    id: 'welcomeModal.earnRewards',
    defaultMessage: 'Earn Rewards',
  },
  shareAppDescription: {
    id: 'welcomeModal.shareAppDescription',
    defaultMessage: 'Share this app to get up to 5% of revenue from each other user trades and get the points to buy gifts for free',
  },
  shareReferralLink: {
    id: 'welcomeModal.shareReferralLink',
    defaultMessage: 'Share referral link',
  },
  sharing: {
    id: 'welcomeModal.sharing',
    defaultMessage: 'Sharing...',
  },
  skip: {
    id: 'welcomeModal.skip',
    defaultMessage: 'Skip',
  },
  iSharedIt: {
    id: 'welcomeModal.iSharedIt',
    defaultMessage: 'I shared it',
  },
  shareTitle: {
    id: 'welcomeModal.shareTitle',
    defaultMessage: 'Join the Marketplace',
  },
  shareText: {
    id: 'welcomeModal.shareText',
    defaultMessage: 'Join me on the marketplace and start trading gifts!',
  },
  linkSharedSuccessfully: {
    id: 'welcomeModal.linkSharedSuccessfully',
    defaultMessage: 'Referral link shared successfully',
  },
  linkCopiedToClipboard: {
    id: 'welcomeModal.linkCopiedToClipboard',
    defaultMessage: 'Referral link copied to clipboard',
  },
  failedToShareLink: {
    id: 'welcomeModal.failedToShareLink',
    defaultMessage: 'Failed to share referral link',
  },
});
