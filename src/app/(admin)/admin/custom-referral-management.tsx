'use client';

import { Plus, Users } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { ManageReferralDialog } from './custom-referral-management/manage-custom-referral-dialog';
import { CustomReferralTable } from './custom-referral-management/custom-referral-table';
import type { UserWithCustomReferral } from './custom-referral-management/custom-referral-types';
import { useCustomReferralManagement } from './custom-referral-management/use-custom-referral-management';

export const CustomReferralManagement = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<UserWithCustomReferral | null>(
    null,
  );

  const {
    usersWithCustomReferrals,
    maxPurchaseFee,
    isLoading,
    loadUsersWithCustomReferrals,
    removeCustomReferralFee,
  } = useCustomReferralManagement();

  const handleEditUser = (user: UserWithCustomReferral) => {
    setEditingUser(user);
    setIsDialogOpen(true);
  };

  const handleAddUser = () => {
    setEditingUser(null);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingUser(null);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Users className="h-4 w-4" />
            Custom Referral Fees
          </CardTitle>
          <CardDescription className="text-sm">
            Set custom referral fees for specific users
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin h-6 w-6 border-2 border-gray-300 border-t-gray-600 rounded-full"></div>
            <span className="ml-2">Loading...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Users className="h-4 w-4" />
          Custom Referral Fees
        </CardTitle>
        <CardDescription className="text-sm">
          Set custom referral fees for specific users
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Max allowed: {maxPurchaseFee} BPS (Purchase Fee)
            </p>
            <Button size="sm" onClick={handleAddUser}>
              <Plus className="h-4 w-4 mr-2" />
              Add Custom Fee
            </Button>
          </div>

          <CustomReferralTable
            users={usersWithCustomReferrals}
            onEditUser={handleEditUser}
            onRemoveUser={removeCustomReferralFee}
          />
        </div>

        <ManageReferralDialog
          isOpen={isDialogOpen}
          onClose={handleCloseDialog}
          onSuccess={loadUsersWithCustomReferrals}
          maxPurchaseFee={maxPurchaseFee}
          editingUser={editingUser}
        />
      </CardContent>
    </Card>
  );
};
